/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  darkMode: 'class', // Dark mode için class stratejisi
  theme: {
    extend: {
      colors: {
        // Figma Design System Colors
        'neutral': {
          'white': '#FFFFFF',
          'black': '#202325',
          100: '#F5F5F5',
          300: '#C6C7C8',
          400: '#A2A4A4',
          500: '#797B7C',
          600: '#636566',
          700: '#37383A',
          800: '#292C2D',
        },
        'primary': {
          100: '#D9E7F7',
          300: '#679DDF',
          400: '#357DD5',
          500: '#025CCA',
          600: '#0253B6',
        },
        'secondary': {
          100: '#F0F8FF',
          200: '#DCEEFE',
        },
        'success': {
          100: '#DCF7EA',
          500: '#50D794',
          600: '#48C185',
          700: '#40AC76',
          800: '#348C60',
          900: '#286B4A',
          'dark': '#2F4B41',
        },
        'warning': {
          100: '#FFEDD5',
          600: '#E49527',
          700: '#CA8522',
        },
        'error': {
          500: '#EE4E4F',
        },
      },
      fontSize: {
        'large-1': ['36px', { lineHeight: '1.3', letterSpacing: '-0.36px' }],
        'title-1': ['24px', { lineHeight: '1.5' }],
        'title-3': ['16px', { lineHeight: '1.5' }],
        'title-4': ['14px', { lineHeight: '1.5' }],
        'title-5': ['12px', { lineHeight: '1.5' }],
        'subtitle-1': ['24px', { lineHeight: '1.5' }],
        'subtitle-3': ['16px', { lineHeight: '1.5' }],
        'subtitle-4': ['14px', { lineHeight: '1.5' }],
        'subtitle-5': ['12px', { lineHeight: '1.5' }],
        'body-4': ['14px', { lineHeight: '1.5' }],
        'body-5': ['12px', { lineHeight: '1.5' }],
      },
      fontWeight: {
        'regular': 400,
        'medium': 500,
        'semibold': 600,
      },
    },
  },
  plugins: [],
}
