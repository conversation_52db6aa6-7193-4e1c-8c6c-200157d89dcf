import React from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import {
  XMarkIcon,
  BuildingStorefrontIcon,
  DocumentChartBarIcon,
  UserGroupIcon,
  CogIcon,
  LanguageIcon,
  QuestionMarkCircleIcon,
  ShieldCheckIcon,
  InformationCircleIcon,
  CubeIcon
} from '@heroicons/react/24/outline';

interface MoreModalProps {
  isOpen: boolean;
  onClose: () => void;
}

interface MenuCardProps {
  icon: React.ReactNode;
  title: string;
  onClick?: () => void;
}

const MenuCard: React.FC<MenuCardProps> = ({ icon, title, onClick }) => {
  return (
    <div
      className="bg-white dark:bg-neutral-black rounded-2xl w-full max-w-[200px] cursor-pointer hover:shadow-lg transition-all duration-200 border border-neutral-100 dark:border-neutral-700 shadow-[0px_4px_20px_0px_rgba(0,0,0,0.05)] hover:scale-105"
      onClick={onClick}
    >
      <div className="flex flex-col gap-3 items-start justify-start px-6 py-4">
        <div className="bg-secondary-100 dark:bg-secondary-100 rounded-2xl w-[51px] h-[51px] flex items-center justify-center flex-shrink-0">
          {icon}
        </div>
        <div className="font-medium text-base text-neutral-black dark:text-neutral-white leading-[1.5] min-w-0">
          {title}
        </div>
      </div>
    </div>
  );
};

export const MoreModal: React.FC<MoreModalProps> = ({ isOpen, onClose }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  if (!isOpen) return null;

  const handleOverlayClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const toolsMenuItems = [
    {
      icon: <BuildingStorefrontIcon className="w-6 h-6 text-primary-500" />,
      title: t('more.outletInformation', 'Outlet Bilgileri'),
      onClick: () => console.log('Outlet Information clicked')
    },
    {
      icon: <DocumentChartBarIcon className="w-6 h-6 text-primary-500" />,
      title: t('more.reportSummary', 'Rapor Özeti'),
      onClick: () => console.log('Report Summary clicked')
    },
    {
      icon: <CubeIcon className="w-6 h-6 text-primary-500" />,
      title: t('more.productManagement', 'Ürün Yönetimi'),
      onClick: () => {
        navigate('/products');
        onClose();
      }
    },
    {
      icon: <UserGroupIcon className="w-6 h-6 text-primary-500" />,
      title: t('more.employeeManagement', 'Çalışan Yönetimi'),
      onClick: () => console.log('Employee Management clicked')
    },
    {
      icon: <CogIcon className="w-6 h-6 text-primary-500" />,
      title: t('more.settings', 'Ayarlar'),
      onClick: () => console.log('Settings clicked')
    },
    {
      icon: <LanguageIcon className="w-6 h-6 text-primary-500" />,
      title: t('more.language', 'Dil'),
      onClick: () => console.log('Language clicked')
    }
  ];

  const helpMenuItems = [
    {
      icon: <QuestionMarkCircleIcon className="w-6 h-6 text-primary-500" />,
      title: t('more.helpCenter', 'Yardım Merkezi'),
      onClick: () => console.log('Help Center clicked')
    },
    {
      icon: <ShieldCheckIcon className="w-6 h-6 text-primary-500" />,
      title: t('more.privacyPolicy', 'Gizlilik Politikası'),
      onClick: () => console.log('Privacy Policy clicked')
    },
    {
      icon: <InformationCircleIcon className="w-6 h-6 text-primary-500" />,
      title: t('more.appInformation', 'Uygulama Bilgileri'),
      onClick: () => console.log('App Information clicked')
    }
  ];

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-20 flex items-center justify-center z-50 p-4"
      onClick={handleOverlayClick}
    >
      <div className="bg-white dark:bg-neutral-black rounded-2xl w-full max-w-[941px] max-h-[90vh] overflow-y-auto mx-auto">
        <div className="flex flex-col gap-5 p-6 pb-7 pt-6">
          {/* Title Bar */}
          <div className="flex items-center justify-between h-11">
            <h2 className="text-2xl font-semibold text-neutral-black dark:text-neutral-white leading-[1.5]">
              {t('navigation.more', 'Daha Fazla')}
            </h2>
            <button
              onClick={onClose}
              className="bg-neutral-100 dark:bg-neutral-700 hover:bg-neutral-200 dark:hover:bg-neutral-600 rounded-full w-10 h-10 flex items-center justify-center transition-colors"
            >
              <XMarkIcon className="w-5 h-5 text-neutral-600 dark:text-neutral-400" />
            </button>
          </div>

          {/* Tools Section */}
          <div className="flex flex-col gap-3.5">
            <h3 className="text-sm font-semibold text-neutral-600 dark:text-neutral-400 leading-[1.5]">
              {t('more.tools', 'Araçlar')}
            </h3>
            <div className="flex flex-wrap gap-4 justify-start">
              {toolsMenuItems.map((item, index) => (
                <MenuCard
                  key={index}
                  icon={item.icon}
                  title={item.title}
                  onClick={item.onClick}
                />
              ))}
            </div>
          </div>

          {/* Help Section */}
          <div className="flex flex-col gap-3.5">
            <h3 className="text-sm font-semibold text-neutral-600 dark:text-neutral-400 leading-[1.5]">
              {t('more.help', 'Yardım')}
            </h3>
            <div className="flex flex-wrap gap-4 justify-start">
              {helpMenuItems.map((item, index) => (
                <MenuCard
                  key={index}
                  icon={item.icon}
                  title={item.title}
                  onClick={item.onClick}
                />
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
