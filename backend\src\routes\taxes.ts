import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { prisma } from '../index';
import { z } from 'zod';
import { TaxType } from '@prisma/client';

// JWT payload tipi
interface JWTPayload {
  id: string;
  role: string;
  branchId: string | null;
  companyId: string;
}

// Fastify instance'ını auth plugin decorators ile genişlet
declare module 'fastify' {
  interface FastifyInstance {
    authenticate: (request: FastifyRequest, reply: FastifyReply) => Promise<void>;
    authorize: (roles: string[]) => (request: FastifyRequest, reply: FastifyReply) => Promise<void>;
  }
}

// Validation şemaları
const createTaxSchema = z.object({
  name: z.string().min(2, 'Vergi adı en az 2 karakter olmalıdır').max(100, 'Vergi adı en fazla 100 karakter olabilir'),
  rate: z.number().min(0, 'Vergi oranı negatif olamaz').max(100, 'Vergi oranı %100\'den fazla olamaz'),
  code: z.string().min(1, 'Vergi kodu zorunludur').max(20, 'Vergi kodu en fazla 20 karakter olabilir').transform(val => val.toUpperCase()),
  type: z.nativeEnum(TaxType, { errorMap: () => ({ message: 'Geçersiz vergi tipi (VAT, OTV, OIV, DAMGA)' }) }),
  isDefault: z.boolean().default(false),
  isIncluded: z.boolean().default(true),
  active: z.boolean().default(true),
});

const updateTaxSchema = createTaxSchema.partial();

const querySchema = z.object({
  page: z.string().transform(val => parseInt(val)).pipe(z.number().int().min(1)).default('1'),
  limit: z.string().transform(val => parseInt(val)).pipe(z.number().int().min(1).max(100)).default('20'),
  search: z.string().optional(),
  type: z.nativeEnum(TaxType).optional(),
  active: z.string().transform(val => val === 'true').pipe(z.boolean()).optional(),
  isDefault: z.string().transform(val => val === 'true').pipe(z.boolean()).optional(),
  isIncluded: z.string().transform(val => val === 'true').pipe(z.boolean()).optional(),
  sortBy: z.enum(['name', 'rate', 'code', 'createdAt']).default('name'),
  sortOrder: z.enum(['asc', 'desc']).default('asc'),
});

export default async function (fastify: FastifyInstance) {
  // Tüm vergi oranlarını listele
  fastify.get('/', {
    onRequest: [fastify.authenticate],
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const user = request.user as JWTPayload;
      const query = querySchema.parse(request.query);

      // Filtreleme koşulları
      const where: any = {
        companyId: user.companyId,
        deletedAt: null,
      };

      if (query.search) {
        where.OR = [
          { name: { contains: query.search, mode: 'insensitive' } },
          { code: { contains: query.search, mode: 'insensitive' } },
        ];
      }

      if (query.type) {
        where.type = query.type;
      }

      if (query.active !== undefined) {
        where.active = query.active;
      }

      if (query.isDefault !== undefined) {
        where.isDefault = query.isDefault;
      }

      if (query.isIncluded !== undefined) {
        where.isIncluded = query.isIncluded;
      }

      // Sıralama
      const orderBy: any = {};
      orderBy[query.sortBy] = query.sortOrder;

      // Sayfalama hesaplamaları
      const skip = (query.page - 1) * query.limit;

      // Toplam kayıt sayısı
      const total = await prisma.tax.count({ where });

      // Vergi oranlarını getir
      const taxes = await prisma.tax.findMany({
        where,
        include: {
          _count: {
            select: {
              products: true,
            },
          },
        },
        orderBy,
        skip,
        take: query.limit,
      });

      // Sayfalama bilgileri
      const totalPages = Math.ceil(total / query.limit);
      const hasNextPage = query.page < totalPages;
      const hasPrevPage = query.page > 1;

      return reply.send({
        success: true,
        data: {
          taxes,
          pagination: {
            page: query.page,
            limit: query.limit,
            total,
            totalPages,
            hasNextPage,
            hasPrevPage,
          },
        },
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(400).send({
        success: false,
        error: 'Bad Request',
        message: error instanceof z.ZodError ? 'Geçersiz sorgu parametreleri' : 'Vergi oranları listelenirken bir hata oluştu',
      });
    }
  });

  // Tek vergi detayı
  fastify.route({
    method: 'GET',
    url: '/:id',
    onRequest: [fastify.authenticate],
    handler: async (request: FastifyRequest<{ Params: { id: string } }>, reply: FastifyReply) => {
    try {
      const user = request.user as JWTPayload;
      const { id } = request.params;

      if (!id || typeof id !== 'string') {
        return reply.status(400).send({
          success: false,
          error: 'Bad Request',
          message: 'Geçersiz vergi ID',
        });
      }

      const tax = await prisma.tax.findFirst({
        where: {
          id,
          companyId: user.companyId,
          deletedAt: null,
        },
        include: {
          products: {
            where: { deletedAt: null },
            select: {
              id: true,
              name: true,
              code: true,
              basePrice: true,
              active: true,
            },
            orderBy: { name: 'asc' },
          },
          _count: {
            select: {
              products: true,
            },
          },
        },
      });

      if (!tax) {
        return reply.status(404).send({
          success: false,
          error: 'Not Found',
          message: 'Vergi oranı bulunamadı',
        });
      }

      return reply.send({
        success: true,
        data: tax,
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({
        success: false,
        error: 'Internal Server Error',
        message: 'Vergi detayı alınırken bir hata oluştu',
      });
    }
    },
  });

  // Yeni vergi oranı oluştur
  fastify.post('/', {
    onRequest: [fastify.authenticate, fastify.authorize(['SUPER_ADMIN', 'ADMIN', 'BRANCH_MANAGER'])],
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const user = request.user as JWTPayload;
      const taxData = createTaxSchema.parse(request.body);

      // Vergi kodu benzersizlik kontrolü
      const existingTax = await prisma.tax.findFirst({
        where: {
          code: taxData.code,
          companyId: user.companyId,
          deletedAt: null,
        },
      });

      if (existingTax) {
        return reply.status(400).send({
          success: false,
          error: 'Bad Request',
          message: 'Bu vergi kodu zaten kullanılıyor',
        });
      }

      // Eğer varsayılan vergi olarak işaretlenmişse, diğer varsayılan vergileri kaldır
      if (taxData.isDefault) {
        await prisma.tax.updateMany({
          where: {
            companyId: user.companyId,
            isDefault: true,
            deletedAt: null,
          },
          data: {
            isDefault: false,
          },
        });
      }

      // Vergi oranını oluştur
      const tax = await prisma.tax.create({
        data: {
          ...taxData,
          companyId: user.companyId,
        },
        include: {
          _count: {
            select: {
              products: true,
            },
          },
        },
      });

      return reply.status(201).send({
        success: true,
        data: tax,
        message: 'Vergi oranı başarıyla oluşturuldu',
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(400).send({
        success: false,
        error: 'Bad Request',
        message: error instanceof z.ZodError ? 'Geçersiz vergi bilgileri' : 'Vergi oranı oluşturulurken bir hata oluştu',
      });
    }
  });

  // Vergi oranı güncelle
  fastify.route({
    method: 'PUT',
    url: '/:id',
    onRequest: [fastify.authenticate, fastify.authorize(['SUPER_ADMIN', 'ADMIN', 'BRANCH_MANAGER'])],
    handler: async (request: FastifyRequest<{ Params: { id: string } }>, reply: FastifyReply) => {
    try {
      const user = request.user as JWTPayload;
      const { id } = request.params;
      const updateData = updateTaxSchema.parse(request.body);

      if (!id || typeof id !== 'string') {
        return reply.status(400).send({
          success: false,
          error: 'Bad Request',
          message: 'Geçersiz vergi ID',
        });
      }

      // Mevcut vergi oranını kontrol et
      const existingTax = await prisma.tax.findFirst({
        where: {
          id,
          companyId: user.companyId,
          deletedAt: null,
        },
      });

      if (!existingTax) {
        return reply.status(404).send({
          success: false,
          error: 'Not Found',
          message: 'Vergi oranı bulunamadı',
        });
      }

      // Vergi kodu benzersizlik kontrolü (eğer güncellenmişse)
      if (updateData.code && updateData.code !== existingTax.code) {
        const codeExists = await prisma.tax.findFirst({
          where: {
            code: updateData.code,
            companyId: user.companyId,
            deletedAt: null,
            NOT: { id },
          },
        });

        if (codeExists) {
          return reply.status(400).send({
            success: false,
            error: 'Bad Request',
            message: 'Bu vergi kodu zaten kullanılıyor',
          });
        }
      }

      // Eğer varsayılan vergi olarak işaretlenmişse, diğer varsayılan vergileri kaldır
      if (updateData.isDefault === true) {
        await prisma.tax.updateMany({
          where: {
            companyId: user.companyId,
            isDefault: true,
            deletedAt: null,
            NOT: { id },
          },
          data: {
            isDefault: false,
          },
        });
      }

      // Vergi oranını güncelle
      const updatedTax = await prisma.tax.update({
        where: { id },
        data: {
          ...updateData,
          version: { increment: 1 }, // Optimistic locking
        },
        include: {
          _count: {
            select: {
              products: true,
            },
          },
        },
      });

      return reply.send({
        success: true,
        data: updatedTax,
        message: 'Vergi oranı başarıyla güncellendi',
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(400).send({
        success: false,
        error: 'Bad Request',
        message: error instanceof z.ZodError ? 'Geçersiz vergi bilgileri' : 'Vergi oranı güncellenirken bir hata oluştu',
      });
    }
    },
  });

  // Vergi oranı sil (soft delete)
  fastify.route({
    method: 'DELETE',
    url: '/:id',
    onRequest: [fastify.authenticate, fastify.authorize(['SUPER_ADMIN', 'ADMIN', 'BRANCH_MANAGER'])],
    handler: async (request: FastifyRequest<{ Params: { id: string } }>, reply: FastifyReply) => {
    try {
      const user = request.user as JWTPayload;
      const { id } = request.params;

      if (!id || typeof id !== 'string') {
        return reply.status(400).send({
          success: false,
          error: 'Bad Request',
          message: 'Geçersiz vergi ID',
        });
      }

      // Mevcut vergi oranını kontrol et
      const existingTax = await prisma.tax.findFirst({
        where: {
          id,
          companyId: user.companyId,
          deletedAt: null,
        },
      });

      if (!existingTax) {
        return reply.status(404).send({
          success: false,
          error: 'Not Found',
          message: 'Vergi oranı bulunamadı',
        });
      }

      // Vergi oranının ürünlerde kullanılıp kullanılmadığını kontrol et
      const taxProducts = await prisma.product.findMany({
        where: {
          taxId: id,
          companyId: user.companyId,
          deletedAt: null,
        },
      });

      if (taxProducts.length > 0) {
        return reply.status(400).send({
          success: false,
          error: 'Bad Request',
          message: 'Bu vergi oranı ürünlerde kullanıldığı için silinemez',
        });
      }

      // Varsayılan vergi oranı silinemez
      if (existingTax.isDefault) {
        return reply.status(400).send({
          success: false,
          error: 'Bad Request',
          message: 'Varsayılan vergi oranı silinemez',
        });
      }

      // Soft delete işlemi
      await prisma.tax.update({
        where: { id },
        data: {
          deletedAt: new Date(),
          active: false,
          version: { increment: 1 },
        },
      });

      return reply.send({
        success: true,
        message: 'Vergi oranı başarıyla silindi',
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({
        success: false,
        error: 'Internal Server Error',
        message: 'Vergi oranı silinirken bir hata oluştu',
      });
    }
    },
  });
}
