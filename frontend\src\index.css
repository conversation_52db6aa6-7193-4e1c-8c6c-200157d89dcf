@tailwind base;
@tailwind components;
@tailwind utilities;

/* Figma Design System Variables */
:root {
  /* Font Family */
  font-family: 'Inter', system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  /* Light Theme Colors */
  --color-neutral-white: #FFFFFF;
  --color-neutral-black: #202325;
  --color-neutral-100: #F5F5F5;
  --color-neutral-300: #C6C7C8;
  --color-neutral-400: #A2A4A4;
  --color-neutral-500: #797B7C;
  --color-neutral-600: #636566;
  --color-neutral-700: #37383A;
  --color-neutral-800: #292C2D;

  /* Primary Colors */
  --color-primary-100: #D9E7F7;
  --color-primary-300: #679DDF;
  --color-primary-400: #357DD5;
  --color-primary-500: #025CCA;
  --color-primary-600: #0253B6;

  /* Secondary Colors */
  --color-secondary-100: #F0F8FF;
  --color-secondary-200: #DCEEFE;

  /* Success Colors */
  --color-success-100: #DCF7EA;
  --color-success-500: #50D794;
  --color-success-600: #48C185;
  --color-success-700: #40AC76;
  --color-success-800: #348C60;
  --color-success-900: #286B4A;
  --color-success-dark: #2F4B41;

  /* Warning Colors */
  --color-warning-100: #FFEDD5;
  --color-warning-600: #E49527;
  --color-warning-700: #CA8522;

  /* Error Colors */
  --color-error-500: #EE4E4F;

  /* Typography Sizes */
  --font-size-large-1: 36px;
  --font-size-title-1: 24px;
  --font-size-title-3: 16px;
  --font-size-title-4: 14px;
  --font-size-title-5: 12px;
  --font-size-subtitle-1: 24px;
  --font-size-subtitle-3: 16px;
  --font-size-subtitle-4: 14px;
  --font-size-subtitle-5: 12px;
  --font-size-body-4: 14px;
  --font-size-body-5: 12px;

  /* Line Heights */
  --line-height-large: 1.3;
  --line-height-normal: 1.5;

  /* Font Weights */
  --font-weight-regular: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
}

/* Dark Theme Colors */
.dark {
  --color-neutral-white: #202325;
  --color-neutral-black: #FFFFFF;
  --color-neutral-100: #37383A;
  --color-neutral-300: #636566;
  --color-neutral-400: #797B7C;
  --color-neutral-500: #A2A4A4;
  --color-neutral-600: #C6C7C8;
  --color-neutral-700: #37383A;
  --color-neutral-800: #292C2D;
}

/* Electron full-screen optimizations - CRITICAL */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html, body, #root {
  height: 100vh;
  width: 100vw;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  font-family: 'Inter', system-ui, Avenir, Helvetica, Arial, sans-serif;
}

body {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  overflow-x: hidden;
  background-color: var(--color-neutral-100);
}

.dark body {
  background-color: var(--color-neutral-800);
}

/* Viewport optimizations for Electron */
@media screen and (min-width: 1024px) {
  .electron-layout {
    height: 100vh;
    width: 100vw;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }
}

/* Responsive breakpoints - TAM VIEWPORT KAPLA */
@media screen and (max-width: 1024px) {
  .electron-layout aside {
    width: 320px;
    margin-right: 1rem;
  }
}

@media screen and (max-width: 768px) {
  .electron-layout .flex-1.flex {
    flex-direction: column;
    height: 100vh;
  }

  .electron-layout aside {
    width: 100%;
    height: 40vh;
    order: -1;
    margin: 0;
    border-radius: 0;
  }

  .electron-layout main {
    padding-right: 0;
  }

  .electron-layout .pl-8 {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}

@media screen and (max-width: 640px) {
  .electron-layout header .px-8 {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .electron-layout .grid-cols-1.md\\:grid-cols-3 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
    gap: 1rem;
  }

  .electron-layout .w-\\[218px\\] {
    width: 100%;
  }

  .electron-layout .w-\\[220px\\] {
    width: 100%;
  }

  .electron-layout .w-\\[426px\\] {
    width: 100%;
  }
}

/* Custom Typography Classes */
.text-large-1-semibold {
  font-size: var(--font-size-large-1);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-large);
  letter-spacing: -0.36px;
}

.text-title-1-medium {
  font-size: var(--font-size-title-1);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-normal);
}

.text-title-3-semibold {
  font-size: var(--font-size-title-3);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-normal);
}

.text-title-4-semibold {
  font-size: var(--font-size-title-4);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-normal);
}

.text-title-5-semibold {
  font-size: var(--font-size-title-5);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-normal);
}

.text-subtitle-3-medium {
  font-size: var(--font-size-subtitle-3);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-normal);
}

.text-subtitle-4-medium {
  font-size: var(--font-size-subtitle-4);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-normal);
}

.text-subtitle-5-medium {
  font-size: var(--font-size-subtitle-5);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-normal);
}

.text-body-4-regular {
  font-size: var(--font-size-body-4);
  font-weight: var(--font-weight-regular);
  line-height: var(--line-height-normal);
}

.text-body-5-regular {
  font-size: var(--font-size-body-5);
  font-weight: var(--font-weight-regular);
  line-height: var(--line-height-normal);
}

/* Custom component styles - Borderless Design System */
.pos-card {
  @apply bg-white shadow-lg rounded-lg p-4;
}

.pos-button {
  @apply bg-blue-500 hover:bg-blue-600 text-white font-medium px-4 py-2 rounded;
}

.pos-input {
  @apply rounded px-3 py-2 w-full bg-neutral-100 dark:bg-neutral-800 text-neutral-black dark:text-neutral-white focus:outline-none focus:bg-neutral-200 dark:focus:bg-neutral-700 transition-colors;
}

/* Global Borderless Design System Overrides */
/* Remove all borders from inputs, buttons, and interactive elements */
input, textarea, select, button {
  border: none !important;
  outline: none !important;
}

/* Ensure focus states use background colors instead of borders */
input:focus, textarea:focus, select:focus {
  @apply bg-neutral-200 dark:bg-neutral-700;
}

/* Remove borders from all form elements */
.form-control, .form-input, .form-select, .form-textarea {
  border: none !important;
  @apply bg-neutral-100 dark:bg-neutral-800;
}

/* Remove borders from cards and containers */
.card, .modal, .dropdown, .popover {
  border: none !important;
}

/* Header buttons should use solid background */
.header-button {
  @apply bg-neutral-100 hover:bg-neutral-200 dark:bg-neutral-700 dark:hover:bg-neutral-600;
  border: none !important;
}

/* Table styling without borders */
table, th, td {
  border: none !important;
}

/* Remove any remaining border utilities */
.border, .border-t, .border-b, .border-l, .border-r,
.border-x, .border-y, .border-solid, .border-dashed, .border-dotted {
  border: none !important;
}

/* Print styles */
@media print {
  body * {
    visibility: hidden;
  }
  .printable, .printable * {
    visibility: visible;
  }
  .printable {
    position: absolute;
    left: 0;
    top: 0;
  }
}
