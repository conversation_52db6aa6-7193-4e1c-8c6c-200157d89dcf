import React from 'react';
import { useTranslation } from 'react-i18next';
import { MoreModal } from './MoreModal';
import {
  HomeIcon as HeroHomeIcon,
  DocumentTextIcon as HeroOrdersIcon,
  TableCellsIcon as HeroTableIcon,
  EllipsisHorizontalIcon as HeroMoreIcon
} from '@heroicons/react/24/outline';
import {
  HomeIcon as HeroHomeIconSolid,
  DocumentTextIcon as HeroOrdersIconSolid,
  TableCellsIcon as HeroTableIconSolid,
  EllipsisHorizontalIcon as HeroMoreIconSolid,
  BuildingStorefrontIcon as HeroRestaurantIconSolid
} from '@heroicons/react/24/solid';

interface NavigationBarProps {
  activeTab?: 'home' | 'orders' | 'table' | 'more';
  onTabChange?: (tab: 'home' | 'orders' | 'table' | 'more') => void;
}

export const NavigationBar = ({ activeTab = 'home', onTabChange }: NavigationBarProps) => {
  const { t } = useTranslation();
  const [currentTab, setCurrentTab] = React.useState(activeTab);
  const [isMoreModalOpen, setIsMoreModalOpen] = React.useState(false);

  const handleTabClick = (tab: 'home' | 'orders' | 'table' | 'more') => {
    if (tab === 'more') {
      setIsMoreModalOpen(true);
      return;
    }

    setCurrentTab(tab);
    if (onTabChange) {
      onTabChange(tab);
    }
  };

  React.useEffect(() => {
    setCurrentTab(activeTab);
  }, [activeTab]);

  return (
    <footer className="bg-white dark:bg-neutral-black shadow-[0px_-2px_16px_0px_rgba(0,0,0,0.08)] dark:shadow-[0px_-2px_16px_0px_rgba(255,255,255,0.04)] relative flex-shrink-0 w-full">
      <div className="w-full h-full">
        <div className="flex flex-row items-center justify-center gap-[104px] px-8 py-1.5 relative w-full h-14">
          
          {/* Sol Menu Grubu */}
          <div className="flex flex-row gap-4 items-center h-11 min-w-0 flex-1">
            {/* Home Button */}
            <div 
              className={`flex-1 h-full rounded-2xl flex items-center justify-center cursor-pointer transition-all duration-200 ${
                currentTab === 'home' 
                  ? 'bg-secondary-100 dark:bg-neutral-700' 
                  : 'bg-transparent hover:bg-neutral-100 dark:hover:bg-neutral-800'
              }`}
              onClick={() => handleTabClick('home')}
            >
              <div className="flex flex-row gap-2.5 items-center justify-center px-4 py-[13px] w-full h-full">
                {currentTab === 'home' ? (
                  <HeroHomeIconSolid
                    className="w-7 h-7 flex-shrink-0 text-primary-500 dark:text-white"
                  />
                ) : (
                  <HeroHomeIcon
                    className="w-7 h-7 flex-shrink-0 text-neutral-600 dark:text-neutral-400"
                  />
                )}
                <span className={`text-sm leading-[1.5] whitespace-nowrap font-medium ${
                  currentTab === 'home'
                    ? 'font-semibold text-primary-500 dark:text-white'
                    : 'text-neutral-600 dark:text-neutral-400'
                }`}>
                  {t('navigation.home', 'Ana Sayfa')}
                </span>
              </div>
            </div>

            {/* Orders Button */}
            <div 
              className={`flex-1 h-full rounded-2xl flex items-center justify-center cursor-pointer transition-all duration-200 ${
                currentTab === 'orders' 
                  ? 'bg-secondary-100 dark:bg-neutral-700' 
                  : 'bg-transparent hover:bg-neutral-100 dark:hover:bg-neutral-800'
              }`}
              onClick={() => handleTabClick('orders')}
            >
              <div className="flex flex-row gap-2.5 items-center justify-center px-4 py-4 w-full h-full">
                {currentTab === 'orders' ? (
                  <HeroOrdersIconSolid
                    className="w-7 h-7 flex-shrink-0 text-primary-500 dark:text-white"
                  />
                ) : (
                  <HeroOrdersIcon
                    className="w-7 h-7 flex-shrink-0 text-neutral-600 dark:text-neutral-400"
                  />
                )}
                <span className={`text-sm leading-[1.5] whitespace-nowrap font-medium ${
                  currentTab === 'orders'
                    ? 'font-semibold text-primary-500 dark:text-white'
                    : 'text-neutral-600 dark:text-neutral-400'
                }`}>
                  {t('navigation.orders', 'Siparişler')}
                </span>
              </div>
            </div>
          </div>

          {/* Sağ Menu Grubu */}
          <div className="flex flex-row gap-4 items-center h-11 min-w-0 flex-1">
            {/* Table Button */}
            <div 
              className={`flex-1 h-full rounded-2xl flex items-center justify-center cursor-pointer transition-all duration-200 ${
                currentTab === 'table' 
                  ? 'bg-secondary-100 dark:bg-neutral-700' 
                  : 'bg-transparent hover:bg-neutral-100 dark:hover:bg-neutral-800'
              }`}
              onClick={() => handleTabClick('table')}
            >
              <div className="flex flex-row gap-2.5 items-center justify-center px-4 py-4 w-full h-full">
                {currentTab === 'table' ? (
                  <HeroTableIconSolid
                    className="w-7 h-7 flex-shrink-0 text-primary-500 dark:text-white"
                  />
                ) : (
                  <HeroTableIcon
                    className="w-7 h-7 flex-shrink-0 text-neutral-600 dark:text-neutral-400"
                  />
                )}
                <span className={`text-sm leading-[1.5] whitespace-nowrap font-medium ${
                  currentTab === 'table'
                    ? 'font-semibold text-primary-500 dark:text-white'
                    : 'text-neutral-600 dark:text-neutral-400'
                }`}>
                  {t('navigation.tables', 'Masalar')}
                </span>
              </div>
            </div>

            {/* More Button */}
            <div 
              className={`flex-1 h-full rounded-2xl flex items-center justify-center cursor-pointer transition-all duration-200 ${
                currentTab === 'more' 
                  ? 'bg-secondary-100 dark:bg-neutral-700' 
                  : 'bg-transparent hover:bg-neutral-100 dark:hover:bg-neutral-800'
              }`}
              onClick={() => handleTabClick('more')}
            >
              <div className="flex flex-row gap-2.5 items-center justify-center px-4 py-4 w-full h-full">
                {currentTab === 'more' ? (
                  <HeroMoreIconSolid
                    className="w-7 h-7 flex-shrink-0 text-primary-500 dark:text-white"
                  />
                ) : (
                  <HeroMoreIcon
                    className="w-7 h-7 flex-shrink-0 text-neutral-600 dark:text-neutral-400"
                  />
                )}
                <span className={`text-sm leading-[1.5] whitespace-nowrap font-medium ${
                  currentTab === 'more'
                    ? 'font-semibold text-primary-500 dark:text-white'
                    : 'text-neutral-600 dark:text-neutral-400'
                }`}>
                  {t('navigation.more', 'Daha Fazla')}
                </span>
              </div>
            </div>
          </div>

          {/* Merkez Restaurant Button */}
          <div className="absolute bg-primary-500 left-1/2 rounded-full w-[72px] h-[72px] top-[-28px] -translate-x-1/2 flex items-center justify-center shadow-lg hover:bg-primary-600 transition-colors cursor-pointer z-10">
            <HeroRestaurantIconSolid className="w-10 h-10 text-white flex-shrink-0" />
          </div>
        </div>
      </div>

      {/* More Modal */}
      <MoreModal
        isOpen={isMoreModalOpen}
        onClose={() => setIsMoreModalOpen(false)}
      />
    </footer>
  );
};
