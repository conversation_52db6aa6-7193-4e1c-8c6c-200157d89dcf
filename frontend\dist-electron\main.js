"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var electron_1 = require("electron");
var path = require("path");
var isDev = process.env.NODE_ENV === 'development' || !electron_1.app.isPackaged;
function createWindow() {
    // Ana pencereyi o<PERSON>
    var mainWindow = new electron_1.BrowserWindow({
        width: 1200,
        height: 800,
        minWidth: 800,
        minHeight: 600,
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            preload: path.join(__dirname, 'preload.js'),
        },
        icon: path.join(__dirname, '../assets/icon.png'), // İkon ekleyeceğiz
        show: false, // Hazır olana kadar gizle
        titleBarStyle: 'default',
    });
    // Pencere hazır olduğunda göster
    mainWindow.once('ready-to-show', function () {
        mainWindow.show();
        if (isDev) {
            mainWindow.webContents.openDevTools();
        }
    });
    // URL'yi yükle
    if (isDev) {
        console.log('Development mode: Loading from http://localhost:5173');
        mainWindow.loadURL('http://localhost:5173').catch(function (err) {
            console.error('Failed to load development URL:', err);
        });
    }
    else {
        console.log('Production mode: Loading from file');
        mainWindow.loadFile(path.join(__dirname, '../dist/index.html')).catch(function (err) {
            console.error('Failed to load production file:', err);
        });
    }
    // Menü çubuğunu kaldır (POS uygulaması için)
    electron_1.Menu.setApplicationMenu(null);
}
// Uygulama hazır olduğunda
electron_1.app.whenReady().then(function () {
    createWindow();
    electron_1.app.on('activate', function () {
        if (electron_1.BrowserWindow.getAllWindows().length === 0) {
            createWindow();
        }
    });
});
// Tüm pencereler kapatıldığında
electron_1.app.on('window-all-closed', function () {
    if (process.platform !== 'darwin') {
        electron_1.app.quit();
    }
});
// Güvenlik: Yeni pencere açılmasını engelle
electron_1.app.on('web-contents-created', function (event, contents) {
    contents.setWindowOpenHandler(function (_a) {
        var url = _a.url;
        // Güvenlik için dış linkleri engelle
        return { action: 'deny' };
    });
});
