import { useCallback } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { useToast } from '../providers/ToastProvider';

interface OptimisticUpdateOptions<T> {
  queryKey: any[];
  updateFn: (oldData: T) => T;
  mutationFn: () => Promise<any>;
  successMessage?: string;
  errorMessage?: string;
  onSuccess?: (data: any) => void;
  onError?: (error: any) => void;
}

export const useOptimisticUpdate = <T>() => {
  const queryClient = useQueryClient();
  const { success, error } = useToast();

  const optimisticUpdate = useCallback(async ({
    queryKey,
    updateFn,
    mutationFn,
    successMessage,
    errorMessage,
    onSuccess,
    onError,
  }: OptimisticUpdateOptions<T>) => {
    // Cancel any outgoing refetches
    await queryClient.cancelQueries({ queryKey });

    // Snapshot the previous value
    const previousData = queryClient.getQueryData<T>(queryKey);

    // Optimistically update to the new value
    if (previousData) {
      queryClient.setQueryData<T>(queryKey, updateFn(previousData));
    }

    try {
      // Perform the mutation
      const result = await mutationFn();
      
      // Show success message
      if (successMessage) {
        success({ title: successMessage });
      }
      
      // Call success callback
      if (onSuccess) {
        onSuccess(result);
      }
      
      return result;
    } catch (err: any) {
      // Rollback on error
      if (previousData) {
        queryClient.setQueryData<T>(queryKey, previousData);
      }
      
      // Show error message
      if (errorMessage) {
        error({ title: errorMessage, message: err.message });
      }
      
      // Call error callback
      if (onError) {
        onError(err);
      }
      
      throw err;
    } finally {
      // Always refetch after mutation
      queryClient.invalidateQueries({ queryKey });
    }
  }, [queryClient, success, error]);

  return { optimisticUpdate };
};

// Specific hook for product optimistic updates
export const useOptimisticProductUpdate = () => {
  const { optimisticUpdate } = useOptimisticUpdate();

  const updateProductStatus = useCallback(async (
    productId: string,
    newStatus: boolean,
    queryKey: any[]
  ) => {
    return optimisticUpdate({
      queryKey,
      updateFn: (oldData: any) => {
        if (oldData?.data?.data) {
          return {
            ...oldData,
            data: {
              ...oldData.data,
              data: oldData.data.data.map((product: any) =>
                product.id === productId
                  ? { ...product, active: newStatus }
                  : product
              ),
            },
          };
        }
        return oldData;
      },
      mutationFn: async () => {
        // This would be replaced with actual API call
        return new Promise(resolve => setTimeout(resolve, 500));
      },
      successMessage: 'Ürün durumu güncellendi',
      errorMessage: 'Ürün durumu güncellenirken hata oluştu',
    });
  }, [optimisticUpdate]);

  const updateProductPrice = useCallback(async (
    productId: string,
    newPrice: number,
    queryKey: any[]
  ) => {
    return optimisticUpdate({
      queryKey,
      updateFn: (oldData: any) => {
        if (oldData?.data?.data) {
          return {
            ...oldData,
            data: {
              ...oldData.data,
              data: oldData.data.data.map((product: any) =>
                product.id === productId
                  ? { ...product, basePrice: newPrice }
                  : product
              ),
            },
          };
        }
        return oldData;
      },
      mutationFn: async () => {
        // This would be replaced with actual API call
        return new Promise(resolve => setTimeout(resolve, 500));
      },
      successMessage: 'Ürün fiyatı güncellendi',
      errorMessage: 'Ürün fiyatı güncellenirken hata oluştu',
    });
  }, [optimisticUpdate]);

  return {
    updateProductStatus,
    updateProductPrice,
  };
};
