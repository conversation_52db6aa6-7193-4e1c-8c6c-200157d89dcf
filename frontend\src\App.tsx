import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { useEffect } from 'react';
import { Login } from './pages/Login';
import { Dashboard } from './pages/Dashboard';
import { ProductManagement } from './pages/ProductManagement';
import { useAuthStore } from './lib/store';
import { apiClient } from './lib/api';

function App() {
  const { token } = useAuthStore();

  // Uygulama başladığında token'ı restore et
  useEffect(() => {
    if (token) {
      apiClient.setToken(token);
    }
  }, [token]);

  // Store değişikliklerini dinle ve API client'ı güncelle
  useEffect(() => {
    const unsubscribe = useAuthStore.subscribe((state) => {
      if (state.token) {
        apiClient.setToken(state.token);
      } else {
        apiClient.setToken(null);
      }
    });

    return unsubscribe;
  }, []);

  return (
    <Router>
      <Routes>
        <Route path="/login" element={<Login />} />
        <Route path="/dashboard" element={<Dashboard />} />
        <Route path="/products" element={<ProductManagement />} />
        <Route path="/" element={<Navigate to="/login" replace />} />
      </Routes>
    </Router>
  );
}

export default App;
