import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { prisma } from '../index';
import { z } from 'zod';
import { ProductUnit } from '@prisma/client';

// JWT payload tipi
interface JWTPayload {
  id: string;
  role: string;
  branchId: string | null;
  companyId: string;
}

// Fastify instance'ını auth plugin decorators ile genişlet
declare module 'fastify' {
  interface FastifyInstance {
    authenticate: (request: FastifyRequest, reply: FastifyReply) => Promise<void>;
    authorize: (roles: string[]) => (request: FastifyRequest, reply: FastifyReply) => Promise<void>;
  }
}

// Request'i extend etmek yerine type assertion kullanacağız

// Validation şemaları
const createProductSchema = z.object({
  categoryId: z.string().cuid('Geçersiz kategori ID'),
  code: z.string().min(1, 'Ürün kodu zorunludur').max(50, '<PERSON>rün kodu en fazla 50 karakter olabilir'),
  barcode: z.string().optional(),
  name: z.string().min(2, '<PERSON>rün adı en az 2 karakter olmalıdır').max(200, 'Ürün adı en fazla 200 karakter olabilir'),
  description: z.string().optional(),
  shortDescription: z.string().optional(),
  image: z.string().url('Geçersiz resim URL\'si').or(z.literal('')).optional(),
  images: z.array(z.string().url('Geçersiz resim URL\'si')).default([]),
  basePrice: z.number().positive('Fiyat pozitif bir sayı olmalıdır'),
  taxId: z.string().cuid('Geçersiz vergi ID'),
  costPrice: z.number().min(0, 'Maliyet fiyatı negatif olamaz').optional(),
  profitMargin: z.number().min(0, 'Kar marjı negatif olamaz').max(100, 'Kar marjı %100\'den fazla olamaz').optional(),
  trackStock: z.boolean().default(false),
  unit: z.nativeEnum(ProductUnit).default(ProductUnit.PIECE),
  criticalStock: z.number().min(0, 'Kritik stok negatif olamaz').optional(),
  available: z.boolean().default(true),
  sellable: z.boolean().default(true),
  preparationTime: z.number().int().min(0, 'Hazırlık süresi negatif olamaz').optional(),
  calories: z.number().int().min(0, 'Kalori negatif olamaz').optional(),
  allergens: z.array(z.string()).default([]),
  hasVariants: z.boolean().default(false),
  hasModifiers: z.boolean().default(false),
  showInMenu: z.boolean().default(true),
  featured: z.boolean().default(false),
  displayOrder: z.number().int().min(0, 'Görüntüleme sırası negatif olamaz').default(0),
  active: z.boolean().default(true),
});

const updateProductSchema = createProductSchema.partial();

const querySchema = z.object({
  page: z.string().transform(val => parseInt(val)).pipe(z.number().int().min(1)).default('1'),
  limit: z.string().transform(val => parseInt(val)).pipe(z.number().int().min(1).max(100)).default('20'),
  search: z.string().optional(),
  categoryId: z.string().cuid().optional(),
  available: z.string().transform(val => val === 'true').pipe(z.boolean()).optional(),
  sellable: z.string().transform(val => val === 'true').pipe(z.boolean()).optional(),
  active: z.string().transform(val => val === 'true').pipe(z.boolean()).optional(),
  trackStock: z.string().transform(val => val === 'true').pipe(z.boolean()).optional(),
  sortBy: z.enum(['name', 'basePrice', 'createdAt', 'displayOrder']).default('displayOrder'),
  sortOrder: z.enum(['asc', 'desc']).default('asc'),
});

export default async function (fastify: FastifyInstance) {
  // Tüm ürünleri listele (sayfalama ve filtreleme ile)
  fastify.get('/', {
    onRequest: [fastify.authenticate],
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const user = request.user as JWTPayload;
      const query = querySchema.parse(request.query);

      // Filtreleme koşulları
      const where: any = {
        companyId: user.companyId,
        deletedAt: null,
      };

      if (query.search) {
        where.OR = [
          { name: { contains: query.search, mode: 'insensitive' } },
          { code: { contains: query.search, mode: 'insensitive' } },
          { barcode: { contains: query.search, mode: 'insensitive' } },
        ];
      }

      if (query.categoryId) {
        where.categoryId = query.categoryId;
      }

      if (query.available !== undefined) {
        where.available = query.available;
      }

      if (query.sellable !== undefined) {
        where.sellable = query.sellable;
      }

      if (query.active !== undefined) {
        where.active = query.active;
      }

      if (query.trackStock !== undefined) {
        where.trackStock = query.trackStock;
      }

      // Sıralama
      const orderBy: any = {};
      orderBy[query.sortBy] = query.sortOrder;

      // Sayfalama hesaplamaları
      const skip = (query.page - 1) * query.limit;

      // Toplam kayıt sayısı
      const total = await prisma.product.count({ where });

      // Ürünleri getir
      const products = await prisma.product.findMany({
        where,
        include: {
          category: {
            select: {
              id: true,
              name: true,
              color: true,
              icon: true,
            },
          },
          tax: {
            select: {
              id: true,
              name: true,
              rate: true,
              type: true,
            },
          },
          variants: {
            where: { deletedAt: null },
            select: {
              id: true,
              name: true,
              code: true,
              price: true,
              active: true,
            },
          },
          _count: {
            select: {
              variants: true,
              modifierGroups: true,
            },
          },
        },
        orderBy,
        skip,
        take: query.limit,
      });

      // Sayfalama bilgileri
      const totalPages = Math.ceil(total / query.limit);
      const hasNextPage = query.page < totalPages;
      const hasPrevPage = query.page > 1;

      return reply.send({
        success: true,
        data: {
          products,
          pagination: {
            page: query.page,
            limit: query.limit,
            total,
            totalPages,
            hasNextPage,
            hasPrevPage,
          },
        },
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(400).send({
        success: false,
        error: 'Bad Request',
        message: error instanceof z.ZodError ? 'Geçersiz sorgu parametreleri' : 'Ürünler listelenirken bir hata oluştu',
      });
    }
  });

  // Tek ürün detayı
  fastify.route({
    method: 'GET',
    url: '/:id',
    onRequest: [fastify.authenticate],
    handler: async (request: FastifyRequest<{ Params: { id: string } }>, reply: FastifyReply) => {
    try {
      const user = request.user as JWTPayload;
      const { id } = request.params;

      if (!id || typeof id !== 'string') {
        return reply.status(400).send({
          success: false,
          error: 'Bad Request',
          message: 'Geçersiz ürün ID',
        });
      }

      const product = await prisma.product.findFirst({
        where: {
          id,
          companyId: user.companyId,
          deletedAt: null,
        },
        include: {
          category: {
            select: {
              id: true,
              name: true,
              description: true,
              color: true,
              icon: true,
              showInKitchen: true,
              preparationTime: true,
            },
          },
          tax: {
            select: {
              id: true,
              name: true,
              rate: true,
              type: true,
              isIncluded: true,
            },
          },
          variants: {
            where: { deletedAt: null },
            orderBy: { displayOrder: 'asc' },
          },
          modifierGroups: {
            include: {
              modifierGroup: {
                include: {
                  modifiers: {
                    where: { deletedAt: null },
                    orderBy: { displayOrder: 'asc' },
                  },
                },
              },
            },
          },
          recipes: {
            where: { deletedAt: null },
            include: {
              items: {
                include: {
                  inventoryItem: {
                    select: {
                      id: true,
                      name: true,
                      unit: true,
                      currentStock: true,
                    },
                  },
                },
              },
            },
          },
        },
      });

      if (!product) {
        return reply.status(404).send({
          success: false,
          error: 'Not Found',
          message: 'Ürün bulunamadı',
        });
      }

      return reply.send({
        success: true,
        data: product,
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({
        success: false,
        error: 'Internal Server Error',
        message: 'Ürün detayı alınırken bir hata oluştu',
      });
    }
    },
  });

  // Yeni ürün oluştur
  fastify.post('/', {
    onRequest: [fastify.authenticate, fastify.authorize(['SUPER_ADMIN', 'ADMIN', 'BRANCH_MANAGER'])],
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const user = request.user as JWTPayload;

      // Debug log
      console.log('Product creation request body:', JSON.stringify(request.body, null, 2));

      const productData = createProductSchema.parse(request.body);

      // Kategori kontrolü
      const category = await prisma.category.findFirst({
        where: {
          id: productData.categoryId,
          companyId: user.companyId,
          deletedAt: null,
        },
      });

      if (!category) {
        return reply.status(400).send({
          success: false,
          error: 'Bad Request',
          message: 'Geçersiz kategori',
        });
      }

      // Vergi kontrolü
      const tax = await prisma.tax.findFirst({
        where: {
          id: productData.taxId,
          companyId: user.companyId,
          deletedAt: null,
        },
      });

      if (!tax) {
        return reply.status(400).send({
          success: false,
          error: 'Bad Request',
          message: 'Geçersiz vergi',
        });
      }

      // Ürün kodu benzersizlik kontrolü
      const existingProduct = await prisma.product.findFirst({
        where: {
          code: productData.code,
          companyId: user.companyId,
          deletedAt: null,
        },
      });

      if (existingProduct) {
        return reply.status(400).send({
          success: false,
          error: 'Bad Request',
          message: 'Bu ürün kodu zaten kullanılıyor',
        });
      }

      // Barkod benzersizlik kontrolü (eğer varsa)
      if (productData.barcode) {
        const existingBarcode = await prisma.product.findFirst({
          where: {
            barcode: productData.barcode,
            companyId: user.companyId,
            deletedAt: null,
          },
        });

        if (existingBarcode) {
          return reply.status(400).send({
            success: false,
            error: 'Bad Request',
            message: 'Bu barkod zaten kullanılıyor',
          });
        }
      }

      // Ürünü oluştur
      const product = await prisma.product.create({
        data: {
          ...productData,
          companyId: user.companyId,
        },
        include: {
          category: {
            select: {
              id: true,
              name: true,
              color: true,
              icon: true,
            },
          },
          tax: {
            select: {
              id: true,
              name: true,
              rate: true,
              type: true,
            },
          },
        },
      });

      return reply.status(201).send({
        success: true,
        data: product,
        message: 'Ürün başarıyla oluşturuldu',
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(400).send({
        success: false,
        error: 'Bad Request',
        message: error instanceof z.ZodError ? 'Geçersiz ürün bilgileri' : 'Ürün oluşturulurken bir hata oluştu',
      });
    }
  });

  // Ürün güncelle
  fastify.route({
    method: 'PUT',
    url: '/:id',
    onRequest: [fastify.authenticate, fastify.authorize(['SUPER_ADMIN', 'ADMIN', 'BRANCH_MANAGER'])],
    handler: async (request: FastifyRequest<{ Params: { id: string } }>, reply: FastifyReply) => {
    try {
      const user = request.user as JWTPayload;
      const { id } = request.params;
      const updateData = updateProductSchema.parse(request.body);

      if (!id || typeof id !== 'string') {
        return reply.status(400).send({
          success: false,
          error: 'Bad Request',
          message: 'Geçersiz ürün ID',
        });
      }

      // Mevcut ürünü kontrol et
      const existingProduct = await prisma.product.findFirst({
        where: {
          id,
          companyId: user.companyId,
          deletedAt: null,
        },
      });

      if (!existingProduct) {
        return reply.status(404).send({
          success: false,
          error: 'Not Found',
          message: 'Ürün bulunamadı',
        });
      }

      // Kategori kontrolü (eğer güncellenmişse)
      if (updateData.categoryId) {
        const category = await prisma.category.findFirst({
          where: {
            id: updateData.categoryId,
            companyId: user.companyId,
            deletedAt: null,
          },
        });

        if (!category) {
          return reply.status(400).send({
            success: false,
            error: 'Bad Request',
            message: 'Geçersiz kategori',
          });
        }
      }

      // Vergi kontrolü (eğer güncellenmişse)
      if (updateData.taxId) {
        const tax = await prisma.tax.findFirst({
          where: {
            id: updateData.taxId,
            companyId: user.companyId,
            deletedAt: null,
          },
        });

        if (!tax) {
          return reply.status(400).send({
            success: false,
            error: 'Bad Request',
            message: 'Geçersiz vergi',
          });
        }
      }

      // Ürün kodu benzersizlik kontrolü (eğer güncellenmişse)
      if (updateData.code && updateData.code !== existingProduct.code) {
        const codeExists = await prisma.product.findFirst({
          where: {
            code: updateData.code,
            companyId: user.companyId,
            deletedAt: null,
            NOT: { id },
          },
        });

        if (codeExists) {
          return reply.status(400).send({
            success: false,
            error: 'Bad Request',
            message: 'Bu ürün kodu zaten kullanılıyor',
          });
        }
      }

      // Barkod benzersizlik kontrolü (eğer güncellenmişse)
      if (updateData.barcode && updateData.barcode !== existingProduct.barcode) {
        const barcodeExists = await prisma.product.findFirst({
          where: {
            barcode: updateData.barcode,
            companyId: user.companyId,
            deletedAt: null,
            NOT: { id },
          },
        });

        if (barcodeExists) {
          return reply.status(400).send({
            success: false,
            error: 'Bad Request',
            message: 'Bu barkod zaten kullanılıyor',
          });
        }
      }

      // Ürünü güncelle
      const updatedProduct = await prisma.product.update({
        where: { id },
        data: {
          ...updateData,
          version: { increment: 1 }, // Optimistic locking
        },
        include: {
          category: {
            select: {
              id: true,
              name: true,
              color: true,
              icon: true,
            },
          },
          tax: {
            select: {
              id: true,
              name: true,
              rate: true,
              type: true,
            },
          },
        },
      });

      return reply.send({
        success: true,
        data: updatedProduct,
        message: 'Ürün başarıyla güncellendi',
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(400).send({
        success: false,
        error: 'Bad Request',
        message: error instanceof z.ZodError ? 'Geçersiz ürün bilgileri' : 'Ürün güncellenirken bir hata oluştu',
      });
    }
    },
  });

  // Ürün sil (soft delete)
  fastify.route({
    method: 'DELETE',
    url: '/:id',
    onRequest: [fastify.authenticate, fastify.authorize(['SUPER_ADMIN', 'ADMIN', 'BRANCH_MANAGER'])],
    handler: async (request: FastifyRequest<{ Params: { id: string } }>, reply: FastifyReply) => {
    try {
      const user = request.user as JWTPayload;
      const { id } = request.params;

      if (!id || typeof id !== 'string') {
        return reply.status(400).send({
          success: false,
          error: 'Bad Request',
          message: 'Geçersiz ürün ID',
        });
      }

      // Mevcut ürünü kontrol et
      const existingProduct = await prisma.product.findFirst({
        where: {
          id,
          companyId: user.companyId,
          deletedAt: null,
        },
      });

      if (!existingProduct) {
        return reply.status(404).send({
          success: false,
          error: 'Not Found',
          message: 'Ürün bulunamadı',
        });
      }

      // Ürünün aktif siparişlerde kullanılıp kullanılmadığını kontrol et
      const activeOrderItems = await prisma.orderItem.findFirst({
        where: {
          productId: id,
          order: {
            status: {
              notIn: ['COMPLETED', 'CANCELLED'],
            },
          },
        },
      });

      if (activeOrderItems) {
        return reply.status(400).send({
          success: false,
          error: 'Bad Request',
          message: 'Bu ürün aktif siparişlerde kullanıldığı için silinemez',
        });
      }

      // Soft delete işlemi
      await prisma.product.update({
        where: { id },
        data: {
          deletedAt: new Date(),
          active: false,
          version: { increment: 1 },
        },
      });

      return reply.send({
        success: true,
        message: 'Ürün başarıyla silindi',
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({
        success: false,
        error: 'Internal Server Error',
        message: 'Ürün silinirken bir hata oluştu',
      });
    }
    },
  });

  // Ürün durumunu değiştir (aktif/pasif)
  fastify.route({
    method: 'PATCH',
    url: '/:id/status',
    onRequest: [fastify.authenticate, fastify.authorize(['SUPER_ADMIN', 'ADMIN', 'BRANCH_MANAGER'])],
    handler: async (request: FastifyRequest<{ Params: { id: string }, Body: { active: boolean } }>, reply: FastifyReply) => {
    try {
      const user = request.user as JWTPayload;
      const { id } = request.params;
      const { active } = request.body;

      if (!id || typeof id !== 'string') {
        return reply.status(400).send({
          success: false,
          error: 'Bad Request',
          message: 'Geçersiz ürün ID',
        });
      }

      if (typeof active !== 'boolean') {
        return reply.status(400).send({
          success: false,
          error: 'Bad Request',
          message: 'Durum bilgisi boolean olmalıdır',
        });
      }

      // Mevcut ürünü kontrol et
      const existingProduct = await prisma.product.findFirst({
        where: {
          id,
          companyId: user.companyId,
          deletedAt: null,
        },
      });

      if (!existingProduct) {
        return reply.status(404).send({
          success: false,
          error: 'Not Found',
          message: 'Ürün bulunamadı',
        });
      }

      // Durumu güncelle
      const updatedProduct = await prisma.product.update({
        where: { id },
        data: {
          active,
          version: { increment: 1 },
        },
        select: {
          id: true,
          name: true,
          active: true,
        },
      });

      return reply.send({
        success: true,
        data: updatedProduct,
        message: `Ürün ${active ? 'aktif' : 'pasif'} duruma getirildi`,
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({
        success: false,
        error: 'Internal Server Error',
        message: 'Ürün durumu güncellenirken bir hata oluştu',
      });
    }
    },
  });
}
