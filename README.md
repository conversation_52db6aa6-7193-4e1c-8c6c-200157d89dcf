# Atropos POS - Restoran Yönetim Sistemi

Türkiye şartlarına uygun, çoklu şube destekli restoran POS uygulaması.

## Teknoloji Yığını

### Frontend (Electron + React)
- Electron (masaüstü uygulama)
- React 18 + TypeScript
- Vite (build tool)
- Tailwind CSS
- React Query (TanStack Query)
- React Router
- Zustand (state management)
- React Hook Form + Zod
- i18next (çoklu dil)

### Backend
- Node.js + Fastify
- PostgreSQL + Prisma ORM
- JWT + bcrypt (authentication)
- Socket.io (gerçek zamanlı bildirimler)

## Özellikler
- ✅ Çoklu şube desteği
- ✅ Karanlık/Aydınlık tema
- ✅ Çoklu dil desteği (TR/EN)
- ✅ Kullanıcı yetkilendirme sistemi
- ✅ Gerçek zamanlı mutfak bildirimleri
- ✅ Masaüst<PERSON> uygulaması (Electron)

## <PERSON><PERSON>
```
atroposv1/
├── frontend/          # Electron + React uygulaması
├── backend/           # Node.js + Fastify API
├── docs/              # Dokümantasyon
└── scripts/           # Build ve deployment scriptleri
```

## Kurulum
1. `npm install` - Tüm bağımlılıkları yükle
2. `npm run dev` - Development modunda başlat
3. `npm run build` - Production build
4. `npm run dev:electron` - Masaüstü uygulamasını geliştirme modunda başlat

## Geliştirme
- Frontend: `npm run dev:frontend`
- Backend: `npm run dev:backend`
- Database: `npm run db:studio`
