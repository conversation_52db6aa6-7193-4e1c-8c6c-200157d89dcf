import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { ThemeMode } from '../lib/types';
import { useSettingsStore } from '../lib/store';
import { MoonIcon, SunIcon } from '@heroicons/react/24/outline';

export function ThemeToggle() {
  const { t } = useTranslation();
  const { theme, setTheme } = useSettingsStore();

  // HTML class özniteliğini güncelle
  useEffect(() => {
    if (theme === ThemeMode.DARK) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, [theme]);

  const toggleTheme = () => {
    const newTheme = theme === ThemeMode.LIGHT ? ThemeMode.DARK : ThemeMode.LIGHT;
    setTheme(newTheme);
  };

  return (
    <div className="flex items-center">
      <button
        onClick={toggleTheme}
        className="p-2 rounded-full hover:bg-neutral-100 dark:hover:bg-neutral-700 transition-colors"
        aria-label={t('theme.toggleTheme')}
      >
        {theme === ThemeMode.LIGHT ? (
          <MoonIcon className="w-6 h-6 text-neutral-600 dark:text-neutral-300" />
        ) : (
          <SunIcon className="w-6 h-6 text-neutral-300" />
        )}
      </button>
    </div>
  );
}
