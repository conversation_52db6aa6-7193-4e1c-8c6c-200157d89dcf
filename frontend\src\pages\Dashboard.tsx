import { useNavigate } from 'react-router-dom';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ThemeToggle } from '../components/ThemeToggle';
import { LanguageSelector } from '../components/LanguageSelector';
import { NavigationBar } from '../components/NavigationBar';
import { useAuthStore } from '../lib/store';
import {
  MagnifyingGlassIcon as SearchIcon,
  BuildingStorefrontIcon as RestaurantIcon,
  CurrencyDollarIcon as DollarIcon,
  ClockIcon as TimerIcon,
  DocumentTextIcon as ArticleIcon,
  DocumentPlusIcon as DocumentAddIcon
} from '@heroicons/react/24/outline';

export function Dashboard() {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { user, isAuthenticated, logout } = useAuthStore();
  const [activeTab, setActiveTab] = useState<'in-progress' | 'waiting-payment'>('in-progress');
  const [searchQuery, setSearchQuery] = useState('');
  const [currentTime, setCurrentTime] = useState('');
  const [currentDate, setCurrentDate] = useState('');

  useEffect(() => {
    // Kullanıcı giriş yapmış mı kontrol et
    if (!isAuthenticated || !user) {
      navigate('/login');
      return;
    }
  }, [isAuthenticated, user, navigate]);

  // Real-time clock effect
  useEffect(() => {
    const updateDateTime = () => {
      const now = new Date();

      const timeString = now.toLocaleTimeString('tr-TR', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });

      const dateString = now.toLocaleDateString('tr-TR', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });

      setCurrentTime(timeString);
      setCurrentDate(dateString);
    };

    // İlk güncelleme
    updateDateTime();

    // Her saniye güncelle
    const interval = setInterval(updateDateTime, 1000);

    // Cleanup function - component unmount olduğunda interval'ı temizle
    return () => clearInterval(interval);
  }, []);

  const handleLogout = () => {
    // Store'dan kullanıcıyı çıkar (token da otomatik temizlenir)
    logout();
    // Login sayfasına yönlendir
    navigate('/login');
  };

  if (!user) {
    return (
      <div className="flex justify-center items-center h-screen bg-neutral-100 dark:bg-neutral-800">
        <div className="animate-spin rounded-full h-12 w-12 bg-gradient-to-r from-primary-500 to-transparent"></div>
      </div>
    );
  }

  return (
    <div className="electron-layout h-screen w-screen bg-neutral-100 dark:bg-neutral-800 flex flex-col overflow-hidden">
      {/* Header - Fixed */}
      <header className="bg-white dark:bg-neutral-black shadow-sm flex-shrink-0 z-10">
        <div className="w-full px-8 py-3">
          <div className="flex items-center justify-between h-[52px]">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-primary-500 rounded-lg flex items-center justify-center">
                  <RestaurantIcon className="w-6 h-6 text-white" />
                </div>
                <h1 className="text-lg font-semibold text-neutral-black dark:text-neutral-white">
                  {t('dashboard.appName', 'Bitepoint POS')}
                </h1>
                <span className="text-sm text-neutral-500 dark:text-neutral-400">
                  {t('dashboard.restaurantSystem', 'Restoran Sistemi')}
                </span>
              </div>
              <div className="ml-8 relative">
                <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-neutral-400" />
                <input
                  type="text"
                  placeholder={t('common.search', 'Ara')}
                  className="pl-10 pr-4 py-2 w-64 bg-neutral-100 dark:bg-neutral-800 rounded-lg text-sm text-neutral-600 dark:text-neutral-300 placeholder-neutral-400 outline-none focus:bg-neutral-200 dark:focus:bg-neutral-700 transition-colors"
                />
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="relative">
                <div className="w-8 h-8 bg-error-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-xs font-bold">2</span>
                </div>
              </div>
              <ThemeToggle />
              <LanguageSelector />
              <div className="relative">
                <button
                  className="flex items-center space-x-2 text-neutral-600 dark:text-neutral-300 hover:text-neutral-black dark:hover:text-neutral-white transition-colors"
                  onClick={() => {
                    const dropdown = document.getElementById('user-dropdown');
                    dropdown?.classList.toggle('hidden');
                  }}
                >
                  <div className="w-8 h-8 rounded-full bg-primary-500 text-white flex items-center justify-center text-sm font-medium">
                    {user?.firstName?.charAt(0).toUpperCase() || 'I'}
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium text-neutral-black dark:text-neutral-white">{user?.firstName} {user?.lastName}</div>
                    <div className="text-xs text-neutral-500 dark:text-neutral-400">{t('roles.cashier', 'Kasiyer')}</div>
                  </div>
                </button>
                <div id="user-dropdown" className="hidden absolute right-0 mt-2 w-48 bg-white dark:bg-neutral-black rounded-xl shadow-lg py-1 z-50">
                  <a href="#" className="block px-4 py-2 text-sm text-neutral-600 dark:text-neutral-300 hover:bg-neutral-100 dark:hover:bg-neutral-700 rounded-lg mx-1">{t('navigation.profile', 'Profil')}</a>
                  <a href="#" className="block px-4 py-2 text-sm text-neutral-600 dark:text-neutral-300 hover:bg-neutral-100 dark:hover:bg-neutral-700 rounded-lg mx-1">{t('navigation.settings', 'Ayarlar')}</a>
                  <a href="#" onClick={handleLogout} className="block px-4 py-2 text-sm text-neutral-600 dark:text-neutral-300 hover:bg-neutral-100 dark:hover:bg-neutral-700 rounded-lg mx-1">{t('auth.logout')}</a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content - Flex Layout */}
      <div className="flex-1 flex overflow-hidden bg-neutral-100 dark:bg-neutral-800">
        {/* Left Side - Main Dashboard */}
        <main className="flex-1 overflow-y-auto pr-8">
          <div className="pl-8 pt-8 pb-8">
            {/* Greetings Section */}
            <div className="flex items-center justify-between mb-8">
              <div className="flex flex-col gap-1">
                <h1 className="text-xl font-semibold text-neutral-black dark:text-neutral-white">
                  {t('dashboard.goodMorning', 'Günaydın')}, {user?.firstName || 'İntan'}
                </h1>
                <div className="flex items-center gap-1">
                  <span className="text-neutral-600 dark:text-neutral-300 text-sm">
                    {t('dashboard.bestService', 'Müşterilerinize en iyi hizmeti verin')}
                  </span>
                  <span className="text-sm">😊</span>
                </div>
              </div>
              <div className="flex flex-col items-end text-right">
                <div className="text-2xl font-medium text-neutral-black dark:text-neutral-white">
                  {currentTime}
                </div>
                <div className="text-sm text-neutral-600 dark:text-neutral-300">
                  {currentDate}
                </div>
              </div>
            </div>

            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              {/* Total Earning Card */}
              <div className="bg-white dark:bg-neutral-black rounded-xl p-5 shadow-sm">
                <div className="flex items-start justify-between mb-3">
                  <span className="text-base font-medium text-neutral-black dark:text-neutral-white">
                    {t('dashboard.totalEarning', 'Toplam Kazanç')}
                  </span>
                  <div className="bg-success-100 dark:bg-success-dark w-12 h-12 rounded-xl flex items-center justify-center">
                    <DollarIcon className="w-7 h-7 text-success-600" />
                  </div>
                </div>
                <div className="text-4xl font-semibold text-neutral-black dark:text-neutral-white mb-2">
                  $526
                </div>
                <div className="text-sm">
                  <span className="text-success-700 font-semibold">+3.2%</span>
                  <span className="text-neutral-600 dark:text-neutral-300"> {t('dashboard.thanYesterday', 'dünden')}</span>
                </div>
              </div>

              {/* In Progress Card */}
              <div className="bg-white dark:bg-neutral-black rounded-xl p-5 shadow-sm">
                <div className="flex items-start justify-between mb-3">
                  <span className="text-base font-medium text-neutral-black dark:text-neutral-white">
                    {t('dashboard.inProgress', 'Devam Eden')}
                  </span>
                  <div className="bg-warning-100 w-12 h-12 rounded-xl flex items-center justify-center">
                    <TimerIcon className="w-7 h-7 text-warning-600" />
                  </div>
                </div>
                <div className="text-4xl font-semibold text-neutral-black dark:text-neutral-white mb-2">
                  16
                </div>
                <div className="text-sm">
                  <span className="text-success-700 font-semibold">+2.1%</span>
                  <span className="text-neutral-600 dark:text-neutral-300"> {t('dashboard.thanYesterday', 'dünden')}</span>
                </div>
              </div>

              {/* Waiting List Card */}
              <div className="bg-white dark:bg-neutral-black rounded-xl p-5 shadow-sm">
                <div className="flex items-start justify-between mb-3">
                  <span className="text-base font-medium text-neutral-black dark:text-neutral-white">
                    {t('dashboard.waitingList', 'Bekleme Listesi')}
                  </span>
                  <div className="bg-secondary-200 dark:bg-primary-400 w-12 h-12 rounded-xl flex items-center justify-center">
                    <ArticleIcon className="w-6 h-6 text-primary-500 dark:text-primary-100" />
                  </div>
                </div>
                <div className="text-4xl font-semibold text-neutral-black dark:text-neutral-white mb-2">
                  5
                </div>
                <div className="text-sm">
                  <span className="text-error-500 font-semibold">-1.6%</span>
                  <span className="text-neutral-600 dark:text-neutral-300"> {t('dashboard.thanYesterday', 'dünden')}</span>
                </div>
              </div>
            </div>

            {/* Content Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Popular Dishes Card */}
              <div className="bg-white dark:bg-neutral-black rounded-2xl p-6 shadow-sm h-[381px]">
                <div className="flex items-center justify-between mb-6">
                  <span className="text-base font-semibold text-neutral-black dark:text-neutral-white">
                    {t('dashboard.popularDishes', 'Popüler Yemekler')}
                  </span>
                  <span className="text-xs font-semibold text-primary-500 cursor-pointer hover:text-primary-600">
                    {t('dashboard.viewAll', 'Tümünü Gör')}
                  </span>
                </div>
                <div className="flex items-center justify-center h-48">
                  <span className="text-xs text-neutral-600 dark:text-neutral-300 text-center">
                    {t('dashboard.noDishesToday', 'Bugün henüz hiç yemek satılmadı.')}
                  </span>
                </div>
              </div>

              {/* Out of Stock Card */}
              <div className="bg-white dark:bg-neutral-black rounded-2xl p-6 shadow-sm h-[381px]">
                <div className="flex items-center justify-between mb-6">
                  <span className="text-base font-semibold text-neutral-black dark:text-neutral-white">
                    {t('dashboard.outOfStock', 'Stok Tükendi')}
                  </span>
                  <span className="text-xs font-semibold text-primary-500 cursor-pointer hover:text-primary-600">
                    {t('dashboard.viewAll', 'Tümünü Gör')}
                  </span>
                </div>
                <div className="flex items-center justify-center h-48">
                  <span className="text-xs text-neutral-600 dark:text-neutral-300 text-center">
                    {t('dashboard.allStockReady', 'Tüm stok ürünleri şu anda hazır!')}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </main>

        {/* Right Side - Orders Panel */}
        <aside className="w-[426px] bg-white dark:bg-neutral-black rounded-2xl mr-8 mt-6 mb-6 flex-shrink-0 overflow-y-auto shadow-lg">
          <div className="p-6">
            {/* Tabs */}
            <div className="bg-neutral-100 dark:bg-neutral-800 h-[52px] rounded-xl flex overflow-hidden mb-6">
              <button
                onClick={() => setActiveTab('in-progress')}
                className={`flex-1 h-full rounded-xl flex items-center justify-center px-3 py-1.5 transition-colors ${
                  activeTab === 'in-progress'
                    ? 'bg-secondary-200 dark:bg-neutral-700 shadow-sm'
                    : 'bg-neutral-100 dark:bg-neutral-800'
                }`}
              >
                <span className={`text-sm font-semibold text-center ${
                  activeTab === 'in-progress'
                    ? 'text-primary-500 dark:text-neutral-white'
                    : 'text-neutral-500 dark:text-neutral-400'
                }`}>
                  {t('dashboard.inProgress', 'Devam Eden')}
                </span>
              </button>
              <button
                onClick={() => setActiveTab('waiting-payment')}
                className={`flex-1 h-full rounded-xl flex items-center justify-center px-3 py-1.5 transition-colors ${
                  activeTab === 'waiting-payment'
                    ? 'bg-secondary-200 dark:bg-neutral-700 shadow-sm'
                    : 'bg-neutral-100 dark:bg-neutral-800'
                }`}
              >
                <span className={`text-sm font-medium text-center ${
                  activeTab === 'waiting-payment'
                    ? 'text-primary-500 dark:text-neutral-white'
                    : 'text-neutral-500 dark:text-neutral-400'
                }`}>
                  {t('dashboard.waitingPayment', 'Ödeme Bekliyor')}
                </span>
              </button>
            </div>

            {/* Search Input */}
            <div className="bg-neutral-100 dark:bg-neutral-800 h-11 rounded-xl flex items-center mb-6">
              <div className="flex items-center gap-3 h-11 px-4 w-full">
                <SearchIcon className="w-5 h-5 text-neutral-500 dark:text-neutral-400" />
                <input
                  type="text"
                  placeholder={t('dashboard.searchOrder', 'Sipariş Ara')}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="flex-1 bg-transparent text-sm text-neutral-500 dark:text-neutral-400 placeholder-neutral-500 dark:placeholder-neutral-400 outline-none"
                />
              </div>
            </div>

            {/* Empty State */}
            <div className="flex flex-col items-center justify-center py-16">
              <div className="bg-secondary-100 dark:bg-primary-400 w-32 h-32 rounded-full flex items-center justify-center mb-6">
                <DocumentAddIcon className="w-12 h-12 text-primary-500 dark:text-primary-100" />
              </div>
              <div className="flex flex-col items-center gap-2 text-center mb-6">
                <span className="text-base font-semibold text-neutral-black dark:text-white">
                  {t('dashboard.noOrders', 'Hiç Siparişiniz Yok')}
                </span>
                <span className="text-sm text-neutral-600 dark:text-neutral-300 max-w-[266px]">
                  {t('dashboard.noOrdersToday', 'Bugün müşterilerinizden henüz sipariş almadınız.')}
                </span>
              </div>
              <button className="bg-primary-500 hover:bg-primary-600 text-white px-6 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors">
                <span className="text-xs font-semibold">
                  {t('dashboard.createNewOrder', 'Yeni Sipariş Oluştur')}
                </span>
              </button>
            </div>
          </div>
        </aside>
      </div>

      {/* Bottom Navigation */}
      <NavigationBar activeTab="home" />
    </div>
  );
}
