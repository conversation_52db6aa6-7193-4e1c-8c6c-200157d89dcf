import React, { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import {
  MagnifyingGlassIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  CheckCircleIcon,
  XCircleIcon,
  StarIcon,
} from '@heroicons/react/24/outline';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Select } from '../ui/Select';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/Card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../ui/Table';
import { useTaxes, useDeleteTax } from '../../hooks/useTaxes';
import type { TaxQueryParams, Tax, TaxType } from '../../types/api';
import { formatDate } from '../../lib/utils';

interface TaxListProps {
  onAddTax: () => void;
  onEditTax: (tax: Tax) => void;
}

export const TaxList: React.FC<TaxListProps> = ({
  onAddTax,
  onEditTax,
}) => {
  const { t } = useTranslation();
  
  // State
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState<TaxType | ''>('');
  const [activeFilter, setActiveFilter] = useState<boolean | ''>('');
  const [currentPage, setCurrentPage] = useState(1);
  const [sortBy, setSortBy] = useState<'name' | 'rate' | 'code' | 'createdAt'>('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');

  // Query parameters
  const queryParams: TaxQueryParams = useMemo(() => ({
    page: currentPage,
    limit: 20,
    search: searchTerm || undefined,
    type: typeFilter || undefined,
    active: activeFilter !== '' ? activeFilter : undefined,
    sortBy,
    sortOrder,
  }), [currentPage, searchTerm, typeFilter, activeFilter, sortBy, sortOrder]);

  // Queries
  const { data: taxesResponse, isLoading, error } = useTaxes(queryParams);
  const deleteTax = useDeleteTax();



  // Handlers
  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
    setCurrentPage(1);
  };

  const handleTypeFilterChange = (value: string) => {
    setTypeFilter(value as TaxType | '');
    setCurrentPage(1);
  };

  const handleActiveFilterChange = (value: string) => {
    setActiveFilter(value === '' ? '' : value === 'true');
    setCurrentPage(1);
  };

  const handleSort = (field: 'name' | 'rate' | 'code' | 'createdAt') => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('asc');
    }
    setCurrentPage(1);
  };

  const handleDelete = async (tax: Tax) => {
    if (window.confirm(t('taxes.confirmDelete'))) {
      try {
        await deleteTax.mutateAsync(tax.id);
      } catch (error) {
        // Error is handled by the mutation
      }
    }
  };

  const getSortIcon = (field: string) => {
    if (sortBy !== field) return null;
    return sortOrder === 'asc' ? '↑' : '↓';
  };

  const handleView = (tax: any) => {
    console.log('View tax:', tax);
    // View functionality can be implemented later
  };

  const getTaxTypeLabel = (type: TaxType) => {
    return t(`taxes.taxTypes.${type}`);
  };

  // Filter options
  const typeOptions = [
    { value: '', label: t('common.all') },
    { value: 'VAT', label: t('taxes.taxTypes.VAT') },
    { value: 'OTV', label: t('taxes.taxTypes.OTV') },
    { value: 'OIV', label: t('taxes.taxTypes.OIV') },
    { value: 'DAMGA', label: t('taxes.taxTypes.DAMGA') },
  ];

  const activeOptions = [
    { value: '', label: t('common.all') },
    { value: 'true', label: t('taxes.active') },
    { value: 'false', label: t('taxes.inactive') },
  ];

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-error-500">
            {t('common.error')}: {error.message}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>{t('taxes.taxList')}</CardTitle>
          <Button onClick={onAddTax} className="flex items-center gap-2">
            <PlusIcon className="w-4 h-4" />
            {t('taxes.addTax')}
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {/* Filters */}
        <div className="flex flex-col gap-4 mb-6">
          {/* Search - Full width on mobile */}
          <div className="w-full">
            <Input
              placeholder={t('taxes.searchTaxes')}
              leftIcon={<MagnifyingGlassIcon className="w-4 h-4" />}
              onChange={(e) => handleSearchChange(e.target.value)}
            />
          </div>

          {/* Filters - Responsive grid */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <Select
              label={t('taxes.taxType')}
              options={typeOptions}
              value={typeFilter}
              onChange={handleTypeFilterChange}
            />
            <Select
              label={t('common.status')}
              options={activeOptions}
              value={activeFilter.toString()}
              onChange={handleActiveFilterChange}
            />
          </div>
        </div>

        {/* Table */}
        <div className="rounded-md border border-neutral-200 dark:border-neutral-700">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead 
                  className="cursor-pointer hover:bg-neutral-50 dark:hover:bg-neutral-800"
                  onClick={() => handleSort('name')}
                >
                  {t('taxes.taxName')} {getSortIcon('name')}
                </TableHead>
                <TableHead 
                  className="cursor-pointer hover:bg-neutral-50 dark:hover:bg-neutral-800"
                  onClick={() => handleSort('code')}
                >
                  {t('taxes.taxCode')} {getSortIcon('code')}
                </TableHead>
                <TableHead 
                  className="cursor-pointer hover:bg-neutral-50 dark:hover:bg-neutral-800"
                  onClick={() => handleSort('rate')}
                >
                  {t('taxes.taxRate')} {getSortIcon('rate')}
                </TableHead>
                <TableHead>{t('taxes.taxType')}</TableHead>
                <TableHead>{t('taxes.isIncluded')}</TableHead>
                <TableHead>{t('taxes.productCount')}</TableHead>
                <TableHead>{t('common.status')}</TableHead>
                <TableHead 
                  className="cursor-pointer hover:bg-neutral-50 dark:hover:bg-neutral-800"
                  onClick={() => handleSort('createdAt')}
                >
                  {t('taxes.createdAt')} {getSortIcon('createdAt')}
                </TableHead>
                <TableHead className="text-right">{t('taxes.actions')}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {isLoading ? (
                <TableRow>
                  <TableCell colSpan={9} className="text-center py-8">
                    {t('common.loading')}
                  </TableCell>
                </TableRow>
              ) : (taxesResponse as any)?.taxes?.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={9} className="text-center py-8">
                    {t('taxes.noTaxesFound')}
                  </TableCell>
                </TableRow>
              ) : (
                (taxesResponse as any)?.taxes?.map((tax: any) => (
                  <TableRow key={tax.id}>
                    <TableCell className="font-medium">
                      <div className="flex items-center gap-2">
                        {tax.isDefault && (
                          <StarIcon className="w-4 h-4 text-warning-600" />
                        )}
                        {tax.name}
                      </div>
                    </TableCell>
                    <TableCell>{tax.code}</TableCell>
                    <TableCell>%{tax.rate}</TableCell>
                    <TableCell>{getTaxTypeLabel(tax.type)}</TableCell>
                    <TableCell>
                      {tax.isIncluded ? t('taxes.inclusive') : t('taxes.exclusive')}
                    </TableCell>
                    <TableCell>{tax._count?.products || 0}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {tax.active ? (
                          <CheckCircleIcon className="w-4 h-4 text-success-500" />
                        ) : (
                          <XCircleIcon className="w-4 h-4 text-error-500" />
                        )}
                        <span className={tax.active ? 'text-success-700' : 'text-error-700'}>
                          {tax.active ? t('taxes.active') : t('taxes.inactive')}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>{formatDate(tax.createdAt)}</TableCell>
                    <TableCell className="text-right">
                      <div className="flex items-center justify-end gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleView(tax)}
                        >
                          <EyeIcon className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onEditTax(tax)}
                        >
                          <PencilIcon className="w-4 h-4" />
                        </Button>
                        {/* Toggle Status - Temporarily disabled */}
                        {/* <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleToggleStatus(tax)}
                          disabled={toggleStatus.isPending}
                        >
                          {tax.active ? (
                            <XCircleIcon className="w-4 h-4 text-error-500" />
                          ) : (
                            <CheckCircleIcon className="w-4 h-4 text-success-500" />
                          )}
                        </Button> */}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDelete(tax)}
                          disabled={deleteTax.isPending}
                        >
                          <TrashIcon className="w-4 h-4 text-error-500" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>

        {/* Pagination */}
        {taxesResponse?.pagination && taxesResponse.pagination.totalPages > 1 && (
          <div className="flex items-center justify-between mt-4">
            <div className="text-sm text-neutral-500">
              Toplam {taxesResponse.pagination.total} vergi
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                disabled={currentPage === 1}
              >
                {t('common.previous')}
              </Button>
              <span className="text-sm">
                {currentPage} / {taxesResponse.pagination.totalPages}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => prev + 1)}
                disabled={currentPage >= taxesResponse.pagination.totalPages}
              >
                {t('common.next')}
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
