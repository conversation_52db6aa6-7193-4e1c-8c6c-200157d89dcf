import React, { useEffect } from 'react';
import { use<PERSON><PERSON>, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useTranslation } from 'react-i18next';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Select } from '../ui/Select';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/Card';
import { Modal } from '../ui/Modal';
import {
  productFormSchema,
  getDefaultProductFormValues,
  productToFormData
} from '../../schemas/productSchema';
import type { ProductFormData } from '../../schemas/productSchema';
import { 
  useCreateProduct, 
  useUpdateProduct, 
  useCategoriesHierarchy, 
  useActiveTaxes 
} from '../../hooks/useProducts';
import type { Product } from '../../types/api';
import { ProductUnit } from '../../types/api';
import { formatCurrency } from '../../lib/utils';

interface ProductFormProps {
  isOpen: boolean;
  onClose: () => void;
  product?: Product | null;
  mode: 'create' | 'edit';
}

export const ProductForm: React.FC<ProductFormProps> = React.memo(({
  isOpen,
  onClose,
  product,
  mode,
}) => {
  const { t } = useTranslation();
  
  // Form setup
  const {
    control,
    handleSubmit,
    reset,
    watch,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm({
    resolver: zodResolver(productFormSchema),
    defaultValues: getDefaultProductFormValues(),
  });

  // Watch values for calculations
  const basePrice = watch('basePrice');
  const costPrice = watch('costPrice');
  const profitMargin = watch('profitMargin');
  const trackStock = watch('trackStock');

  // Data fetching
  const { data: categoriesResponse } = useCategoriesHierarchy();
  const { data: taxesResponse } = useActiveTaxes();
  
  // Mutations
  const createProductMutation = useCreateProduct();
  const updateProductMutation = useUpdateProduct();

  // Reset form when product changes
  useEffect(() => {
    if (isOpen) {
      if (mode === 'edit' && product) {
        reset(productToFormData(product));
      } else {
        reset(getDefaultProductFormValues());
      }
    }
  }, [isOpen, mode, product, reset]);

  // Calculate profit margin when prices change
  useEffect(() => {
    if (basePrice && costPrice && basePrice > 0 && costPrice > 0) {
      const calculatedMargin = ((basePrice - costPrice) / basePrice) * 100;
      if (Math.abs(calculatedMargin - (profitMargin || 0)) > 0.01) {
        setValue('profitMargin', Math.round(calculatedMargin * 100) / 100);
      }
    }
  }, [basePrice, costPrice, profitMargin, setValue]);

  // Calculate base price when cost price and margin change
  const handleMarginChange = (margin: number) => {
    if (costPrice && margin >= 0 && margin < 100) {
      const calculatedPrice = costPrice / (1 - margin / 100);
      setValue('basePrice', Math.round(calculatedPrice * 100) / 100);
    }
  };

  const onSubmit = async (data: ProductFormData) => {
    try {
      if (mode === 'create') {
        await createProductMutation.mutateAsync(data);
      } else if (mode === 'edit' && product) {
        await updateProductMutation.mutateAsync({ id: product.id, data });
      }
      onClose();
    } catch (error) {
      // Error handling is done in the mutation hooks
    }
  };

  // Category options
  const categoryOptions = React.useMemo(() => {
    if (!categoriesResponse?.success) return [];

    // Always work with an array
    const categories = Array.isArray(categoriesResponse.data)
      ? categoriesResponse.data
      : (categoriesResponse.data as any)?.categories ?? [];

    const options: { value: string; label: string }[] = [];

    const addCategoryOptions = (list: any[] = [], level = 0) => {
      if (!Array.isArray(list)) return;

      list.forEach((category) => {
        const prefix = '  '.repeat(level);
        options.push({
          value: category.id,
          label: `${prefix}${category.name}`,
        });
        if (Array.isArray(category.children) && category.children.length) {
          addCategoryOptions(category.children, level + 1);
        }
      });
    };

    addCategoryOptions(categories);
    return options;
  }, [categoriesResponse]);

  // Tax options
  const taxOptions = React.useMemo(() => {
    if (!taxesResponse) return [];

    // Response format: { taxes: [...], pagination: {...} }
    const taxes = (taxesResponse as any)?.taxes || [];

    if (!Array.isArray(taxes)) return [];

    return taxes.map((tax: any) => ({
      value: tax.id,
      label: `${tax.name} (${tax.rate}%)`,
    }));
  }, [taxesResponse]);

  // Unit options
  const unitOptions = Object.values(ProductUnit).map(unit => ({
    value: unit,
    label: t(`products.units.${unit.toLowerCase()}`, unit),
  }));

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={mode === 'create' ? t('products.addProduct') : t('products.editProduct')}
      size="lg"
      className="mx-4 max-h-[90vh] overflow-y-auto"
    >
      <form onSubmit={handleSubmit(onSubmit as any)} className="space-y-6">
        {/* Temel Bilgiler */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">{t('products.basicInfo')}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Controller
                name="name"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    label={t('products.productName')}
                    error={errors.name?.message}
                    required
                  />
                )}
              />
              
              <Controller
                name="code"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    label={t('products.productCode')}
                    error={errors.code?.message}
                    required
                  />
                )}
              />
            </div>

            <Controller
              name="barcode"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  label={t('products.barcode')}
                  error={errors.barcode?.message}
                />
              )}
            />

            <Controller
              name="shortDescription"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  label={t('products.shortDescription')}
                  error={errors.shortDescription?.message}
                />
              )}
            />

            <Controller
              name="description"
              control={control}
              render={({ field }) => (
                <div>
                  <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1">
                    {t('products.description')}
                  </label>
                  <textarea
                    {...field}
                    rows={3}
                    className="w-full rounded-md border border-neutral-300 dark:border-neutral-600 bg-neutral-100 dark:bg-neutral-800 px-3 py-2 text-sm text-neutral-black dark:text-neutral-white focus:border-primary-500 focus:ring-primary-500 focus:outline-none focus:ring-2 transition-colors"
                  />
                  {errors.description && (
                    <p className="mt-1 text-sm text-error-500">{errors.description.message}</p>
                  )}
                </div>
              )}
            />
          </CardContent>
        </Card>

        {/* Kategori ve Vergi */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">{t('products.categoryAndTax')}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Controller
                name="categoryId"
                control={control}
                render={({ field }) => (
                  <Select
                    {...field}
                    label={t('products.category')}
                    options={categoryOptions}
                    placeholder={t('products.selectCategory')}
                    error={errors.categoryId?.message}
                    required
                  />
                )}
              />

              <Controller
                name="taxId"
                control={control}
                render={({ field }) => (
                  <Select
                    {...field}
                    label={t('products.tax')}
                    options={taxOptions}
                    placeholder={t('products.selectTax')}
                    error={errors.taxId?.message}
                    required
                  />
                )}
              />
            </div>
          </CardContent>
        </Card>

        {/* Fiyat Bilgileri */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">{t('products.priceInfo')}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Controller
                name="basePrice"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    type="number"
                    step="0.01"
                    min="0"
                    label={t('products.price')}
                    error={errors.basePrice?.message}
                    required
                    onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                  />
                )}
              />

              <Controller
                name="costPrice"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    type="number"
                    step="0.01"
                    min="0"
                    label={t('products.cost')}
                    error={errors.costPrice?.message}
                    onChange={(e) => field.onChange(parseFloat(e.target.value) || undefined)}
                  />
                )}
              />

              <Controller
                name="profitMargin"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    type="number"
                    step="0.01"
                    min="0"
                    max="100"
                    label={t('products.profitMargin')}
                    error={errors.profitMargin?.message}
                    onChange={(e) => {
                      const value = parseFloat(e.target.value) || 0;
                      field.onChange(value);
                      handleMarginChange(value);
                    }}
                  />
                )}
              />
            </div>

            {basePrice > 0 && (
              <div className="text-sm text-neutral-600 dark:text-neutral-400">
                {t('products.finalPrice')}: <strong>{formatCurrency(basePrice)}</strong>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Stok Bilgileri */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">{t('products.stockInfo')}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-2">
              <Controller
                name="trackStock"
                control={control}
                render={({ field }) => (
                  <input
                    type="checkbox"
                    checked={field.value}
                    onChange={field.onChange}
                    className="rounded border-neutral-300 dark:border-neutral-600"
                  />
                )}
              />
              <label className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
                {t('products.trackStock')}
              </label>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Controller
                name="unit"
                control={control}
                render={({ field }) => (
                  <Select
                    {...field}
                    label={t('products.unit')}
                    options={unitOptions}
                    error={errors.unit?.message}
                  />
                )}
              />

              {trackStock && (
                <Controller
                  name="criticalStock"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      type="number"
                      min="0"
                      label={t('products.minStock')}
                      error={errors.criticalStock?.message}
                      onChange={(e) => field.onChange(parseFloat(e.target.value) || undefined)}
                    />
                  )}
                />
              )}
            </div>
          </CardContent>
        </Card>

        {/* Durum Ayarları */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">{t('products.statusSettings')}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="flex items-center space-x-2">
                <Controller
                  name="active"
                  control={control}
                  render={({ field }) => (
                    <input
                      type="checkbox"
                      checked={field.value}
                      onChange={field.onChange}
                      className="rounded border-neutral-300 dark:border-neutral-600"
                    />
                  )}
                />
                <label className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
                  {t('products.active')}
                </label>
              </div>

              <div className="flex items-center space-x-2">
                <Controller
                  name="available"
                  control={control}
                  render={({ field }) => (
                    <input
                      type="checkbox"
                      checked={field.value}
                      onChange={field.onChange}
                      className="rounded border-neutral-300 dark:border-neutral-600"
                    />
                  )}
                />
                <label className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
                  {t('products.available')}
                </label>
              </div>

              <div className="flex items-center space-x-2">
                <Controller
                  name="sellable"
                  control={control}
                  render={({ field }) => (
                    <input
                      type="checkbox"
                      checked={field.value}
                      onChange={field.onChange}
                      className="rounded border-neutral-300 dark:border-neutral-600"
                    />
                  )}
                />
                <label className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
                  {t('products.sellable')}
                </label>
              </div>

              <div className="flex items-center space-x-2">
                <Controller
                  name="showInMenu"
                  control={control}
                  render={({ field }) => (
                    <input
                      type="checkbox"
                      checked={field.value}
                      onChange={field.onChange}
                      className="rounded border-neutral-300 dark:border-neutral-600"
                    />
                  )}
                />
                <label className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
                  {t('products.showInMenu')}
                </label>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Form Actions */}
        <div className="flex justify-end space-x-4">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            disabled={isSubmitting}
          >
            {t('common.cancel')}
          </Button>
          <Button
            type="submit"
            loading={isSubmitting}
            disabled={isSubmitting}
          >
            {mode === 'create' ? t('common.create') : t('common.update')}
          </Button>
        </div>
      </form>
    </Modal>
  );
});
